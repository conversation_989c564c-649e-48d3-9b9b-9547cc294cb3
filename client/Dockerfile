# Stage 1: Build Angular and API
FROM --platform=$BUILDPLATFORM node:20-alpine AS builder

WORKDIR /app

COPY ../ ./

RUN npm install

RUN npm run build

# Stage 2: Run
FROM node:20-alpine

WORKDIR /app

COPY --from=builder /app/dist /app/dist
COPY --from=builder /app/package.json /app/package.json
COPY --from=builder /app/client/src/api/templates /app/dist/proposals/templates

RUN npm install --omit=dev

EXPOSE 4000

CMD ["node", "dist/proposals/server/server.mjs"]
