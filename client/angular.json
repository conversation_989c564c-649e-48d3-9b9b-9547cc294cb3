{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"proposals": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "chm", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/proposals", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/api/templates", "output": "templates"}], "styles": ["src/styles.css", "node_modules/aos/dist/aos.css"], "scripts": [], "server": "src/main.server.ts", "outputMode": "server", "ssr": {"entry": "src/server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "proposals:build:production"}, "development": {"buildTarget": "proposals:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/api/templates", "output": "templates"}], "styles": ["src/styles.css"], "scripts": []}}}}}}